name: mobby
description: "Mobby is a mobile app for the vehicles spair parts and accessories and services."
publish_to: 'none' 
version: 1.0.0+1
environment:
  sdk: ^3.8.1
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

  # State Management
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1

  # Navigation
  go_router: ^15.2.3

  # Network & Storage
  dio: ^5.7.0
  connectivity_plus: ^6.1.4
  shared_preferences: ^2.5.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # UI & Images
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2

  # Utilities
  equatable: ^2.0.7
  dartz: ^0.10.1
  intl: ^0.19.0
  json_annotation: ^4.9.0

  # Firebase
  firebase_core: ^2.24.0
  firebase_auth: ^4.7.0
  cloud_firestore: ^4.8.0
  firebase_storage: ^11.2.0
  firebase_messaging: ^14.6.0
  flex_color_scheme: ^8.2.0
  flutter_screenutil: ^5.9.3
  shimmer: ^3.0.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  riverpod_generator: ^2.6.2
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1
flutter:
  uses-material-design: true

  
  
  
  

  
  

  
  

  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
