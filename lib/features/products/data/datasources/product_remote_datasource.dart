import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product_model.dart';
import '../models/brand_model.dart';
import '../models/review_model.dart';
import '../../domain/entities/product_filter.dart';
import '../../domain/entities/product_search_result.dart';

abstract class ProductRemoteDataSource {
  Future<ProductModel?> getProductById(String id);
  Future<ProductSearchResult> getProducts(ProductFilter filter);
  Future<List<ProductModel>> getFeaturedProducts({int limit = 10});
  Future<List<ProductModel>> getPopularProducts({int limit = 10});
  Future<List<ProductModel>> getNewProducts({int limit = 10});
  Future<List<ProductModel>> getSaleProducts({int limit = 10});

  Future<ProductSearchResult> getProductsByCategory(
    String categoryId, {
    ProductFilter? filter,
  });
  Future<List<ProductModel>> getRelatedProducts(
    String productId, {
    int limit = 5,
  });
  Future<List<ProductModel>> getFrequentlyBoughtTogether(
    String productId, {
    int limit = 3,
  });

  Future<ProductSearchResult> getProductsByBrand(
    String brandId, {
    ProductFilter? filter,
  });
  Future<List<BrandModel>> getBrands();
  Future<List<BrandModel>> getFeaturedBrands({int limit = 10});
  Future<BrandModel?> getBrandById(String id);

  Future<ProductSearchResult> searchProducts(
    String query, {
    ProductFilter? filter,
  });
  Future<List<String>> getSearchSuggestions(String query, {int limit = 5});

  Future<List<ReviewModel>> getProductReviews(
    String productId, {
    int page = 1,
    int limit = 20,
  });
  Future<ReviewModel?> getReviewById(String id);
  Future<ReviewModel> createReview(ReviewModel review);
  Future<ReviewModel> updateReview(ReviewModel review);
  Future<void> deleteReview(String id);
  Future<void> markReviewHelpful(String reviewId, bool isHelpful);

  Future<bool> isProductInStock(String productId);
  Future<int> getProductStock(String productId);
  Future<List<ProductModel>> getLowStockProducts({int limit = 20});
  Future<List<ProductModel>> getOutOfStockProducts({int limit = 20});

  Future<List<ProductModel>> getProductsForVehicle(
    String vehicleModel, {
    ProductFilter? filter,
  });
  Future<List<String>> getCompatibleVehicles(String productId);

  Future<ProductSearchResult> getProductsInPriceRange(
    double minPrice,
    double maxPrice, {
    ProductFilter? filter,
  });
  Future<List<ProductModel>> getProductsOnSale({
    ProductFilter? filter,
    int limit = 20,
  });

  Future<void> trackProductView(String productId);
  Future<void> trackProductSearch(String query);
  Future<void> trackCategoryView(String categoryId);
  Future<void> trackBrandView(String brandId);

  Future<List<ProductModel>> getProductsByIds(List<String> productIds);
  Future<Map<String, bool>> checkProductsAvailability(List<String> productIds);
  Future<Map<String, int>> getProductsStock(List<String> productIds);
}

class ProductRemoteDataSourceImpl implements ProductRemoteDataSource {
  final FirebaseFirestore _firestore;

  ProductRemoteDataSourceImpl({FirebaseFirestore? firestore})
    : _firestore = firestore ?? FirebaseFirestore.instance;
}
