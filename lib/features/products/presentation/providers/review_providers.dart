import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/review.dart';

part 'review_providers.g.dart';

// Product Reviews Provider - fetches reviews for a specific product
@riverpod
Future<List<Review>> productReviews(
  ProductReviewsRef ref,
  String productId, {
  int page = 1,
  int limit = 20,
}) async {
  // Simulate network delay
  await Future.delayed(const Duration(milliseconds: 500));
  
  // Mock review data - in a real app this would fetch from repository
  return _generateMockReviews(productId, limit);
}

// Helper function to generate mock reviews
List<Review> _generateMockReviews(String productId, int count) {
  final reviews = <Review>[];
  final now = DateTime.now();
  
  for (int i = 0; i < count; i++) {
    reviews.add(Review(
      id: 'review_${productId}_$i',
      productId: productId,
      userId: 'user_$i',
      userName: _getMockUserName(i),
      userAvatarUrl: null,
      rating: _getMockRating(i),
      title: _getMockReviewTitle(i),
      comment: _getMockReviewComment(i),
      imageUrls: i % 3 == 0 ? _getMockImageUrls() : [],
      isVerifiedPurchase: i % 2 == 0,
      isHelpful: false,
      helpfulCount: (i * 3) % 15,
      notHelpfulCount: (i * 2) % 8,
      createdAt: now.subtract(Duration(days: i * 2)),
      updatedAt: now.subtract(Duration(days: i * 2)),
    ));
  }
  
  return reviews;
}

String _getMockUserName(int index) {
  final names = [
    'John Smith',
    'Sarah Johnson',
    'Mike Wilson',
    'Emily Davis',
    'David Brown',
    'Lisa Garcia',
    'Chris Martinez',
    'Amanda Taylor',
    'Ryan Anderson',
    'Jessica White',
  ];
  return names[index % names.length];
}

double _getMockRating(int index) {
  final ratings = [5.0, 4.0, 5.0, 3.0, 4.0, 5.0, 2.0, 4.0, 5.0, 3.0];
  return ratings[index % ratings.length];
}

String _getMockReviewTitle(int index) {
  final titles = [
    'Excellent product!',
    'Good quality for the price',
    'Amazing features',
    'Could be better',
    'Highly recommended',
    'Perfect for my needs',
    'Not what I expected',
    'Great value',
    'Outstanding quality',
    'Decent product',
  ];
  return titles[index % titles.length];
}

String _getMockReviewComment(int index) {
  final comments = [
    'This product exceeded my expectations. The quality is outstanding and it works perfectly for my needs. I would definitely recommend it to others.',
    'Good product overall. The build quality is solid and it does what it\'s supposed to do. The price point is reasonable for what you get.',
    'I\'m really impressed with this purchase. The features are exactly what I was looking for and the performance is excellent.',
    'The product is okay but I was expecting a bit more. It works fine but there are some minor issues that could be improved.',
    'Absolutely love this product! It has made my life so much easier and the quality is top-notch. Worth every penny.',
    'Perfect fit for what I needed. The design is sleek and the functionality is exactly as described. Very satisfied with this purchase.',
    'Unfortunately, this product didn\'t meet my expectations. While it works, there are several issues that make it less than ideal.',
    'Great value for money. The product quality is good and it serves its purpose well. Would consider buying again.',
    'Outstanding quality and performance. This product has been a game-changer for me. Highly recommend to anyone considering it.',
    'Decent product that gets the job done. Nothing spectacular but it works as advertised and the price is fair.',
  ];
  return comments[index % comments.length];
}

List<String> _getMockImageUrls() {
  return [
    'https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=Review+Image+1',
    'https://via.placeholder.com/300x300/4ECDC4/FFFFFF?text=Review+Image+2',
    'https://via.placeholder.com/300x300/45B7D1/FFFFFF?text=Review+Image+3',
  ];
}

// Review Summary Provider - provides aggregated review data
@riverpod
Future<ReviewSummary> reviewSummary(
  ReviewSummaryRef ref,
  String productId,
) async {
  final reviews = await ref.watch(productReviewsProvider(productId).future);
  
  if (reviews.isEmpty) {
    return const ReviewSummary(
      averageRating: 0.0,
      totalReviews: 0,
      ratingDistribution: {},
    );
  }
  
  final totalReviews = reviews.length;
  final totalRating = reviews.fold<double>(0.0, (sum, review) => sum + review.rating);
  final averageRating = totalRating / totalReviews;
  
  // Calculate rating distribution
  final ratingDistribution = <int, int>{};
  for (int i = 1; i <= 5; i++) {
    ratingDistribution[i] = reviews.where((r) => r.rating.round() == i).length;
  }
  
  return ReviewSummary(
    averageRating: averageRating,
    totalReviews: totalReviews,
    ratingDistribution: ratingDistribution,
  );
}

// Review Summary data class
class ReviewSummary {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution;
  
  const ReviewSummary({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
  });
}
